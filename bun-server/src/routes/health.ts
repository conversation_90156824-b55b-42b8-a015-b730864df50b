import { ElysiaApp } from "../server.ts";

export default (app: ElysiaApp) =>
  app.get("/", () => ({
    status: "ok",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: "1.0.0"
  }), {
    detail: {
      tags: ["Health"],
      summary: "Health Check",
      description: "Returns the health status of the server",
      responses: {
        200: {
          description: "Server is healthy",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  status: {
                    type: "string",
                    example: "ok",
                    description: "Health status"
                  },
                  timestamp: {
                    type: "string",
                    format: "date-time",
                    description: "Current server timestamp"
                  },
                  uptime: {
                    type: "number",
                    description: "Server uptime in seconds"
                  },
                  version: {
                    type: "string",
                    example: "1.0.0",
                    description: "API version"
                  }
                }
              }
            }
          }
        }
      }
    }
  })