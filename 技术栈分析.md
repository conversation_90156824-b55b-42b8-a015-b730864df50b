# Claude Code UI 项目技术栈分析

## 项目概述

Claude Code UI 是一个为 [Claude Code CLI](https://docs.anthropic.com/en/docs/claude-code) 和 [Cursor CLI](https://docs.cursor.com/en/cli/overview) 提供 Web 界面的项目。它提供了桌面和移动端的响应式界面，让用户可以通过浏览器与 Claude Code 和 Cursor CLI 进行交互。

## 核心技术栈

### 前端技术栈

#### 主要框架和库
- **React 18** - 现代化的用户界面库，使用 Hooks 架构
- **React Router DOM 6.8.1** - 客户端路由管理
- **Vite 7.0.4** - 现代化的构建工具和开发服务器，提供快速的热重载

#### UI 组件和样式
- **Tailwind CSS 3.4.0** - 实用优先的 CSS 框架
- **@tailwindcss/typography** - Tailwind 的排版插件
- **Lucide React** - 现代化的图标库
- **class-variance-authority** - 类型安全的样式变体管理
- **clsx** - 条件类名工具
- **tailwind-merge** - Tailwind 类名合并工具

#### 代码编辑器
- **CodeMirror 6** - 高级代码编辑器
  - `@codemirror/lang-css` - CSS 语言支持
  - `@codemirror/lang-html` - HTML 语言支持
  - `@codemirror/lang-javascript` - JavaScript 语言支持
  - `@codemirror/lang-json` - JSON 语言支持
  - `@codemirror/lang-markdown` - Markdown 语言支持
  - `@codemirror/lang-python` - Python 语言支持
  - `@codemirror/theme-one-dark` - 暗色主题
- **@uiw/react-codemirror** - React 的 CodeMirror 包装器

#### 终端模拟器
- **xterm 5.3.0** - 浏览器中的终端模拟器
- **xterm-addon-fit** - 终端自适应大小插件
- **@xterm/addon-clipboard** - 剪贴板支持
- **@xterm/addon-webgl** - WebGL 渲染加速

#### 文件处理
- **react-dropzone** - 拖拽文件上传组件
- **react-markdown** - Markdown 渲染组件

### 后端技术栈

#### 核心框架
- **Node.js** - JavaScript 运行时环境
- **Express 4.18.2** - Web 应用框架
- **WebSocket (ws 8.14.2)** - 实时双向通信

#### 数据库
- **better-sqlite3 12.2.0** - SQLite 数据库驱动
- **sqlite3 5.1.7** - SQLite 数据库

#### 进程管理和系统交互
- **node-pty 1.1.0-beta34** - 伪终端接口，用于创建和管理子进程
- **cross-spawn 7.0.3** - 跨平台进程启动
- **chokidar 4.0.3** - 文件系统监控

#### 安全和认证
- **bcrypt 6.0.0** - 密码哈希
- **jsonwebtoken 9.0.2** - JWT 令牌处理
- **cors 2.8.5** - 跨域资源共享

#### 文件处理
- **multer 2.0.1** - 多部分表单数据处理（文件上传）
- **mime-types 3.0.1** - MIME 类型检测

#### 网络请求
- **node-fetch 2.7.0** - HTTP 请求库

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │  Claude CLI     │
│   (React/Vite)  │◄──►│ (Express/WS)    │◄──►│  Integration    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 前端架构

#### 组件结构
- **App.jsx** - 主应用组件，包含路由和状态管理
- **MainContent.jsx** - 主内容区域
- **Sidebar.jsx** - 侧边栏，显示项目和会话列表
- **ChatInterface.jsx** - 聊天界面
- **Shell.jsx** - 终端模拟器组件
- **FileTree.jsx** - 文件浏览器
- **GitPanel.jsx** - Git 操作面板

#### 状态管理
- 使用 React Hooks 进行状态管理
- Context API 用于主题和认证状态
- WebSocket 连接管理实时数据同步

### 后端架构

#### 服务器结构
- **server/index.js** - 主服务器文件
- **server/claude-cli.js** - Claude CLI 集成
- **server/cursor-cli.js** - Cursor CLI 集成
- **server/projects.js** - 项目管理
- **server/routes/** - API 路由模块
- **server/middleware/** - 中间件
- **server/database/** - 数据库操作

#### API 设计
- RESTful API 用于项目和会话管理
- WebSocket 用于实时通信
- 文件系统 API 用于文件操作

## Claude Code 调用机制

### 核心调用方式

项目通过以下方式调用 Claude Code：

#### 1. 命令行集成 (server/claude-cli.js)

```javascript
// 使用 cross-spawn 启动 Claude CLI 进程
const claudeProcess = spawnFunction('claude', args, {
  cwd: workingDir,
  stdio: ['pipe', 'pipe', 'pipe'],
  env: { ...process.env }
});
```

#### 2. 参数构建
- `--print` - 打印模式，直接传递命令
- `--resume <sessionId>` - 恢复会话
- `--output-format stream-json` - 流式 JSON 输出
- `--verbose` - 详细输出
- `--model sonnet` - 指定模型
- `--mcp-config` - MCP 服务器配置

#### 3. 双重交互模式

**A. 聊天界面模式**
- 通过 WebSocket 发送 `claude-command` 消息
- 服务器启动 Claude CLI 进程
- 流式接收 JSON 响应并转发给前端

**B. 终端模式**
- 通过 Shell 组件直接连接到 Claude CLI
- 使用 node-pty 创建伪终端
- 提供完整的终端交互体验

### 会话管理

#### 会话生命周期
1. **创建会话** - 用户发送消息时自动创建
2. **会话保护** - 活跃会话期间暂停项目更新
3. **会话恢复** - 通过 `--resume` 参数恢复历史会话
4. **会话清理** - 进程结束时清理临时文件

#### 数据流

```
用户输入 → WebSocket → Express Server → Claude CLI → 流式输出 → WebSocket → 前端显示
```

### 文件系统集成

#### 项目发现
- 自动扫描 `~/.claude/projects/` 目录
- 使用 chokidar 监控文件系统变化
- 实时更新项目列表

#### 文件操作
- 读取/写入项目文件
- 文件树浏览
- 语法高亮显示

## 特色功能

### 1. 响应式设计
- 桌面和移动端适配
- PWA 支持，可添加到主屏幕

### 2. 实时同步
- WebSocket 实现实时通信
- 文件系统监控自动更新

### 3. 多 CLI 支持
- 同时支持 Claude Code 和 Cursor CLI
- 统一的界面体验

### 4. 安全机制
- JWT 认证
- 工具权限控制
- 默认禁用危险操作

### 5. 开发体验
- 热重载开发环境
- TypeScript 类型支持
- 现代化构建工具

## 部署和配置

### 开发模式
```bash
npm run dev  # 同时启动前端和后端
```

### 生产模式
```bash
npm run build  # 构建前端
npm run start  # 启动生产服务器
```

### 环境配置
- `.env` 文件配置端口和其他环境变量
- 支持 OpenAI API 集成（语音转录）
- MCP 服务器配置支持

## 总结

Claude Code UI 是一个现代化的全栈 Web 应用，它巧妙地将 Claude Code CLI 包装成了一个用户友好的 Web 界面。通过 Node.js 后端作为桥梁，实现了浏览器与命令行工具的无缝集成。项目采用了当前最佳实践的技术栈，提供了出色的开发和用户体验。

**不是传统意义上的终端**，而是一个完整的 Web 应用，它提供了：
- 图形化的聊天界面
- 嵌入式终端模拟器
- 文件管理器
- Git 操作界面
- 项目和会话管理

这种设计让用户可以在任何设备上通过浏览器访问 Claude Code 的全部功能，大大提升了可访问性和易用性。
