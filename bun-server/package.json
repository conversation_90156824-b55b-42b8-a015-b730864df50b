{"name": "bun-server", "type": "module", "scripts": {"dev": "bun --watch src/index.ts", "start": "NODE_ENV=production bun run ./src/index.ts"}, "dependencies": {"@elysiajs/cors": "^1.3.1", "@elysiajs/jwt": "^1.3.3", "@elysiajs/static": "^1.3.0", "@elysiajs/swagger": "^1.3.0", "bcryptjs": "^3.0.2", "elysia": "^1.3.1", "elysia-autoload": "^1.5.1", "env-var": "^7.5.0", "mime-types": "^3.0.1", "nanoid": "^5.1.5", "node-pty": "^1.0.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/bun": "^1.2.13"}}