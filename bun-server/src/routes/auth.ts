import { Elysia, t } from "elysia";
import { userDb, db } from "../database/db.ts";
import { hashPassword, verifyPassword, generateToken, jwtConfig } from "../middleware/auth.ts";

export default new Elysia()
  .use(jwtConfig)
  
  // Check auth status and setup requirements
  .get("/status", async () => {
    try {
      const hasUsers = userDb.hasUsers();
      return { 
        needsSetup: !hasUsers,
        isAuthenticated: false // Will be overridden by frontend if token exists
      };
    } catch (error) {
      console.error('Auth status error:', error);
      throw new Error('Internal server error');
    }
  }, {
    detail: {
      tags: ["Authentication"],
      summary: "Check Authentication Status",
      description: "Check if the system needs setup and authentication status"
    }
  })

  // Register new user (only if no users exist)
  .post("/register", async ({ body, jwt, set }) => {
    try {
      const { username, password } = body as { username: string; password: string };
      
      if (!username || !password) {
        set.status = 400;
        return { error: 'Username and password are required' };
      }
      
      if (password.length < 6) {
        set.status = 400;
        return { error: 'Password must be at least 6 characters long' };
      }
      
      // Use a transaction to prevent race conditions
      const hasUsers = userDb.hasUsers();
      if (hasUsers) {
        set.status = 403;
        return { error: 'User already exists. This is a single-user system.' };
      }
      
      // Hash password
      const passwordHash = await hashPassword(password);
      
      // Create user
      const user = userDb.createUser(username, passwordHash);
      
      // Generate token
      const token = await generateToken(user, jwt);
      
      // Update last login
      userDb.updateLastLogin(user.id);
      
      return {
        success: true,
        user: { id: user.id, username: user.username },
        token
      };
      
    } catch (error: any) {
      console.error('Registration error:', error);
      if (error.message?.includes('UNIQUE constraint failed')) {
        set.status = 409;
        return { error: 'Username already exists' };
      } else {
        set.status = 500;
        return { error: 'Internal server error' };
      }
    }
  }, {
    body: t.Object({
      username: t.String({ minLength: 1 }),
      password: t.String({ minLength: 6 })
    }),
    detail: {
      tags: ["Authentication"],
      summary: "Register New User",
      description: "Register a new user (only allowed if no users exist)"
    }
  })

  // Login user
  .post("/login", async ({ body, jwt, set }) => {
    try {
      const { username, password } = body as { username: string; password: string };
      
      if (!username || !password) {
        set.status = 400;
        return { error: 'Username and password are required' };
      }
      
      // Get user by username
      const user = userDb.getUserByUsername(username) as any;
      if (!user) {
        set.status = 401;
        return { error: 'Invalid credentials' };
      }
      
      // Verify password
      const isValidPassword = await verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        set.status = 401;
        return { error: 'Invalid credentials' };
      }
      
      // Generate token
      const token = await generateToken(user, jwt);
      
      // Update last login
      userDb.updateLastLogin(user.id);
      
      return {
        success: true,
        user: { id: user.id, username: user.username },
        token
      };
      
    } catch (error) {
      console.error('Login error:', error);
      set.status = 500;
      return { error: 'Internal server error' };
    }
  }, {
    body: t.Object({
      username: t.String({ minLength: 1 }),
      password: t.String({ minLength: 1 })
    }),
    detail: {
      tags: ["Authentication"],
      summary: "User Login",
      description: "Authenticate user and return JWT token"
    }
  })

  // Get current user (protected route)
  .get("/user", async ({ headers, jwt, set }) => {
    const authHeader = headers.authorization;
    const token = authHeader?.split(' ')[1];

    if (!token) {
      set.status = 401;
      return { error: 'Access denied. No token provided.' };
    }

    try {
      const decoded = await jwt.verify(token);
      const user = userDb.getUserById(decoded.userId);
      
      if (!user) {
        set.status = 401;
        return { error: 'Invalid token. User not found.' };
      }
      
      return { user };
    } catch (error) {
      console.error('Token verification error:', error);
      set.status = 403;
      return { error: 'Invalid token' };
    }
  }, {
    detail: {
      tags: ["Authentication"],
      summary: "Get Current User",
      description: "Get current authenticated user information"
    }
  });
