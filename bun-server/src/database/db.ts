import { Database } from "bun:sqlite";
import { readFileSync, existsSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const DB_PATH = join(__dirname, 'auth.db');
const INIT_SQL_PATH = join(__dirname, 'init.sql');

// Create database connection
const db = new Database(DB_PATH);
console.log('Connected to SQLite database');

// Initialize database with schema
export const initializeDatabase = async () => {
  try {
    if (existsSync(INIT_SQL_PATH)) {
      const initSQL = readFileSync(INIT_SQL_PATH, 'utf8');
      db.exec(initSQL);
      console.log('Database initialized successfully');
    }
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

// User database operations
export const userDb = {
  // Check if any users exist
  hasUsers: (): boolean => {
    try {
      const row = db.query('SELECT COUNT(*) as count FROM users').get() as { count: number };
      return row.count > 0;
    } catch (err) {
      throw err;
    }
  },

  // Create a new user
  createUser: (username: string, passwordHash: string) => {
    try {
      const stmt = db.query('INSERT INTO users (username, password_hash) VALUES (?, ?) RETURNING id, username');
      const result = stmt.get(username, passwordHash) as { id: number; username: string };
      return result;
    } catch (err) {
      throw err;
    }
  },

  // Get user by username
  getUserByUsername: (username: string) => {
    try {
      const row = db.query('SELECT * FROM users WHERE username = ? AND is_active = 1').get(username);
      return row;
    } catch (err) {
      throw err;
    }
  },

  // Update last login time
  updateLastLogin: (userId: number) => {
    try {
      db.query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?').run(userId);
    } catch (err) {
      throw err;
    }
  },

  // Get user by ID
  getUserById: (userId: number) => {
    try {
      const row = db.query('SELECT id, username, created_at, last_login FROM users WHERE id = ? AND is_active = 1').get(userId);
      return row;
    } catch (err) {
      throw err;
    }
  }
};

export { db };
