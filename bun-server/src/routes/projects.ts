import { Elysia, t } from "elysia";
import { authGuard } from "../middleware/auth.ts";

export default new Elysia()
  // Get all projects
  .get("/", async ({ headers, jwt, set }) => {
    // Apply auth guard
    const authResult = await authGuard({ jwt, headers, set });
    if (authResult.error) return authResult;

    try {
      // TODO: Implement project discovery logic
      // This will scan ~/.claude/projects/ and ~/.cursor/chats/
      const projects = [
        {
          name: "example-project",
          displayName: "Example Project",
          path: "/path/to/project",
          fullPath: "/full/path/to/project",
          sessions: [],
          cursorSessions: [],
          sessionMeta: { total: 0 }
        }
      ];
      
      return projects;
    } catch (error) {
      console.error('Error fetching projects:', error);
      set.status = 500;
      return { error: 'Failed to fetch projects' };
    }
  }, {
    detail: {
      tags: ["Projects"],
      summary: "Get All Projects",
      description: "Retrieve all Claude and Cursor projects"
    }
  })

  // Get project sessions
  .get("/:projectName/sessions", async ({ params, query, headers, jwt, set }) => {
    const authResult = await authGuard({ jwt, headers, set });
    if (authResult.error) return authResult;

    try {
      const { projectName } = params;
      const { limit = "5", offset = "0" } = query;
      
      // TODO: Implement session retrieval logic
      const result = {
        sessions: [],
        total: 0,
        hasMore: false
      };
      
      return result;
    } catch (error) {
      console.error('Error fetching sessions:', error);
      set.status = 500;
      return { error: 'Failed to fetch sessions' };
    }
  }, {
    params: t.Object({
      projectName: t.String()
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
      offset: t.Optional(t.String())
    }),
    detail: {
      tags: ["Projects"],
      summary: "Get Project Sessions",
      description: "Retrieve sessions for a specific project"
    }
  })

  // Get session messages
  .get("/:projectName/sessions/:sessionId/messages", async ({ params, query, headers, jwt, set }) => {
    const authResult = await authGuard({ jwt, headers, set });
    if (authResult.error) return authResult;

    try {
      const { projectName, sessionId } = params;
      const { limit, offset } = query;
      
      // TODO: Implement message retrieval logic
      const result = {
        messages: [],
        total: 0,
        hasMore: false
      };
      
      return result;
    } catch (error) {
      console.error('Error fetching messages:', error);
      set.status = 500;
      return { error: 'Failed to fetch messages' };
    }
  }, {
    params: t.Object({
      projectName: t.String(),
      sessionId: t.String()
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
      offset: t.Optional(t.String())
    }),
    detail: {
      tags: ["Projects"],
      summary: "Get Session Messages",
      description: "Retrieve messages for a specific session"
    }
  })

  // Create new project
  .post("/create", async ({ body, headers, jwt, set }) => {
    const authResult = await authGuard({ jwt, headers, set });
    if (authResult.error) return authResult;

    try {
      const { path: projectPath } = body as { path: string };
      
      if (!projectPath || !projectPath.trim()) {
        set.status = 400;
        return { error: 'Project path is required' };
      }
      
      // TODO: Implement project creation logic
      const project = {
        name: "new-project",
        displayName: "New Project",
        path: projectPath,
        fullPath: projectPath
      };
      
      return { success: true, project };
    } catch (error) {
      console.error('Error creating project:', error);
      set.status = 500;
      return { error: 'Failed to create project' };
    }
  }, {
    body: t.Object({
      path: t.String({ minLength: 1 })
    }),
    detail: {
      tags: ["Projects"],
      summary: "Create New Project",
      description: "Create a new project manually"
    }
  });
