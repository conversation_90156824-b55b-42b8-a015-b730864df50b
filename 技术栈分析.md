# Claude Code UI 项目技术栈分析

## 项目概述

Claude Code UI 是一个为 [Claude Code CLI](https://docs.anthropic.com/en/docs/claude-code) 和 [Cursor CLI](https://docs.cursor.com/en/cli/overview) 提供 Web 界面的项目。它提供了桌面和移动端的响应式界面，让用户可以通过浏览器与 Claude Code 和 Cursor CLI 进行交互。

## 核心技术栈

### 前端技术栈

#### 主要框架和库
- **React 18** - 现代化的用户界面库，使用 Hooks 架构
- **React Router DOM 6.8.1** - 客户端路由管理
- **Vite 7.0.4** - 现代化的构建工具和开发服务器，提供快速的热重载

#### UI 组件和样式
- **Tailwind CSS 3.4.0** - 实用优先的 CSS 框架
- **@tailwindcss/typography** - Tailwind 的排版插件
- **Lucide React** - 现代化的图标库
- **class-variance-authority** - 类型安全的样式变体管理
- **clsx** - 条件类名工具
- **tailwind-merge** - Tailwind 类名合并工具

#### 代码编辑器
- **CodeMirror 6** - 高级代码编辑器
  - `@codemirror/lang-css` - CSS 语言支持
  - `@codemirror/lang-html` - HTML 语言支持
  - `@codemirror/lang-javascript` - JavaScript 语言支持
  - `@codemirror/lang-json` - JSON 语言支持
  - `@codemirror/lang-markdown` - Markdown 语言支持
  - `@codemirror/lang-python` - Python 语言支持
  - `@codemirror/theme-one-dark` - 暗色主题
- **@uiw/react-codemirror** - React 的 CodeMirror 包装器

#### 终端模拟器
- **xterm 5.3.0** - 浏览器中的终端模拟器
- **xterm-addon-fit** - 终端自适应大小插件
- **@xterm/addon-clipboard** - 剪贴板支持
- **@xterm/addon-webgl** - WebGL 渲染加速

#### 文件处理
- **react-dropzone** - 拖拽文件上传组件
- **react-markdown** - Markdown 渲染组件

### 后端技术栈

#### 核心框架
- **Node.js** - JavaScript 运行时环境
- **Express 4.18.2** - Web 应用框架
- **WebSocket (ws 8.14.2)** - 实时双向通信

#### 数据库
- **better-sqlite3 12.2.0** - SQLite 数据库驱动
- **sqlite3 5.1.7** - SQLite 数据库

#### 进程管理和系统交互
- **node-pty 1.1.0-beta34** - 伪终端接口，用于创建和管理子进程
- **cross-spawn 7.0.3** - 跨平台进程启动
- **chokidar 4.0.3** - 文件系统监控

#### 安全和认证
- **bcrypt 6.0.0** - 密码哈希
- **jsonwebtoken 9.0.2** - JWT 令牌处理
- **cors 2.8.5** - 跨域资源共享

#### 文件处理
- **multer 2.0.1** - 多部分表单数据处理（文件上传）
- **mime-types 3.0.1** - MIME 类型检测

#### 网络请求
- **node-fetch 2.7.0** - HTTP 请求库

## 架构设计

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │  Claude CLI     │
│   (React/Vite)  │◄──►│ (Express/WS)    │◄──►│  Integration    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 前端架构

#### 组件结构
- **App.jsx** - 主应用组件，包含路由和状态管理
- **MainContent.jsx** - 主内容区域
- **Sidebar.jsx** - 侧边栏，显示项目和会话列表
- **ChatInterface.jsx** - 聊天界面
- **Shell.jsx** - 终端模拟器组件
- **FileTree.jsx** - 文件浏览器
- **GitPanel.jsx** - Git 操作面板

#### 状态管理
- 使用 React Hooks 进行状态管理
- Context API 用于主题和认证状态
- WebSocket 连接管理实时数据同步

### 后端架构

#### 服务器结构
- **server/index.js** - 主服务器文件
- **server/claude-cli.js** - Claude CLI 集成
- **server/cursor-cli.js** - Cursor CLI 集成
- **server/projects.js** - 项目管理
- **server/routes/** - API 路由模块
- **server/middleware/** - 中间件
- **server/database/** - 数据库操作

#### API 设计
- RESTful API 用于项目和会话管理
- WebSocket 用于实时通信
- 文件系统 API 用于文件操作

## Claude Code 调用机制

### 核心调用方式

项目通过以下方式调用 Claude Code：

#### 1. 命令行集成 (server/claude-cli.js)

```javascript
// 使用 cross-spawn 启动 Claude CLI 进程
const claudeProcess = spawnFunction('claude', args, {
  cwd: workingDir,
  stdio: ['pipe', 'pipe', 'pipe'],
  env: { ...process.env }
});
```

#### 2. 参数构建
- `--print` - 打印模式，直接传递命令
- `--resume <sessionId>` - 恢复会话
- `--output-format stream-json` - 流式 JSON 输出
- `--verbose` - 详细输出
- `--model sonnet` - 指定模型
- `--mcp-config` - MCP 服务器配置

#### 3. 双重交互模式

**A. 聊天界面模式**
- 通过 WebSocket 发送 `claude-command` 消息
- 服务器启动 Claude CLI 进程
- 流式接收 JSON 响应并转发给前端

**B. 终端模式**
- 通过 Shell 组件直接连接到 Claude CLI
- 使用 node-pty 创建伪终端
- 提供完整的终端交互体验

### 会话管理

#### 会话生命周期
1. **创建会话** - 用户发送消息时自动创建
2. **会话保护** - 活跃会话期间暂停项目更新
3. **会话恢复** - 通过 `--resume` 参数恢复历史会话
4. **会话清理** - 进程结束时清理临时文件

#### 数据流

```
用户输入 → WebSocket → Express Server → Claude CLI → 流式输出 → WebSocket → 前端显示
```

### 文件系统集成

#### 项目发现
- 自动扫描 `~/.claude/projects/` 目录
- 使用 chokidar 监控文件系统变化
- 实时更新项目列表

#### 文件操作
- 读取/写入项目文件
- 文件树浏览
- 语法高亮显示

## 特色功能

### 1. 响应式设计
- 桌面和移动端适配
- PWA 支持，可添加到主屏幕

### 2. 实时同步
- WebSocket 实现实时通信
- 文件系统监控自动更新

### 3. 多 CLI 支持
- 同时支持 Claude Code 和 Cursor CLI
- 统一的界面体验

### 4. 安全机制
- JWT 认证
- 工具权限控制
- 默认禁用危险操作

### 5. 开发体验
- 热重载开发环境
- TypeScript 类型支持
- 现代化构建工具

## 部署和配置

### 开发模式
```bash
npm run dev  # 同时启动前端和后端
```

### 生产模式
```bash
npm run build  # 构建前端
npm run start  # 启动生产服务器
```

### 环境配置
- `.env` 文件配置端口和其他环境变量
- 支持 OpenAI API 集成（语音转录）
- MCP 服务器配置支持

## 总结

Claude Code UI 是一个现代化的全栈 Web 应用，它巧妙地将 Claude Code CLI 包装成了一个用户友好的 Web 界面。通过 Node.js 后端作为桥梁，实现了浏览器与命令行工具的无缝集成。项目采用了当前最佳实践的技术栈，提供了出色的开发和用户体验。

**不是传统意义上的终端**，而是一个完整的 Web 应用，它提供了：
- 图形化的聊天界面
- 嵌入式终端模拟器
- 文件管理器
- Git 操作界面
- 项目和会话管理

这种设计让用户可以在任何设备上通过浏览器访问 Claude Code 的全部功能，大大提升了可访问性和易用性。

## Claude Code 使用详细分析

### 1. Claude Code CLI 调用的核心部分

#### A. 主要调用入口 (server/claude-cli.js)

**核心函数：`spawnClaude(command, options, ws)`**

```javascript
// 构建 Claude CLI 命令参数
const args = [];

// 1. 打印模式参数
if (command && command.trim()) {
  args.push('--print');
  args.push(command);
}

// 2. 会话恢复参数
if (resume && sessionId) {
  args.push('--resume', sessionId);
}

// 3. 基础参数
args.push('--output-format', 'stream-json', '--verbose');

// 4. 模型选择
if (!resume) {
  args.push('--model', 'sonnet');
}

// 5. 权限模式
if (permissionMode && permissionMode !== 'default') {
  args.push('--permission-mode', permissionMode);
}

// 6. 工具设置
if (settings.skipPermissions) {
  args.push('--dangerously-skip-permissions');
} else {
  // 允许的工具
  for (const tool of allowedTools) {
    args.push('--allowedTools', tool);
  }
  // 禁用的工具
  for (const tool of disallowedTools) {
    args.push('--disallowedTools', tool);
  }
}

// 7. MCP 配置
if (hasMcpServers) {
  args.push('--mcp-config', configPath);
}
```

#### B. 进程启动和管理

```javascript
// 启动 Claude CLI 进程
const claudeProcess = spawnFunction('claude', args, {
  cwd: workingDir,
  stdio: ['pipe', 'pipe', 'pipe'],
  env: { ...process.env }
});

// 处理输出流
claudeProcess.stdout.on('data', (data) => {
  // 解析 JSON 响应或原始文本
  const lines = data.toString().split('\n').filter(line => line.trim());
  for (const line of lines) {
    try {
      const response = JSON.parse(line);
      ws.send(JSON.stringify({
        type: 'claude-response',
        data: response
      }));
    } catch (parseError) {
      ws.send(JSON.stringify({
        type: 'claude-output',
        data: line
      }));
    }
  }
});
```

### 2. 项目发现和管理 (server/projects.js)

#### A. Claude 项目发现机制

**扫描 `~/.claude/projects/` 目录：**
- 每个项目是一个目录，名称为项目路径编码（`/` 替换为 `-`）
- 包含 `.jsonl` 文件存储对话历史
- 从 `cwd` 字段提取实际项目路径

```javascript
// 项目发现流程
async function getProjects() {
  const claudeProjectsPath = path.join(process.env.HOME, '.claude', 'projects');
  const projectDirs = await fs.readdir(claudeProjectsPath);

  for (const projectDir of projectDirs) {
    // 从 .jsonl 文件提取项目路径
    const actualPath = await extractProjectDirectory(projectDir);
    // 获取会话列表
    const sessions = await getSessions(projectDir);
  }
}
```

#### B. 会话数据解析

**解析 JSONL 格式的会话文件：**
```javascript
async function getSessions(projectName, limit = 5, offset = 0) {
  const projectPath = path.join(claudeProjectsPath, projectName);
  const files = await fs.readdir(projectPath);
  const jsonlFiles = files.filter(f => f.endsWith('.jsonl'));

  // 解析每个 .jsonl 文件
  for (const file of jsonlFiles) {
    const sessionId = path.basename(file, '.jsonl');
    const messages = await parseJsonlFile(filePath);
    // 提取会话元数据
  }
}
```

### 3. MCP (Model Context Protocol) 集成 (server/routes/mcp.js)

#### A. MCP 服务器管理

**通过 Claude CLI 管理 MCP 服务器：**

```javascript
// 列出 MCP 服务器
router.get('/cli/list', async (req, res) => {
  const process = spawn('claude', ['mcp', 'list'], {
    stdio: ['pipe', 'pipe', 'pipe']
  });
});

// 添加 MCP 服务器
router.post('/cli/add', async (req, res) => {
  const cliArgs = ['mcp', 'add', '--scope', scope, name, type];
  const process = spawn('claude', cliArgs, spawnOptions);
});

// 删除 MCP 服务器
router.delete('/cli/remove/:name', async (req, res) => {
  const process = spawn('claude', ['mcp', 'remove', '--scope', actualScope, actualName]);
});
```

#### B. MCP 配置检测

**自动检测 MCP 配置：**
```javascript
// 检查 ~/.claude.json 中的 MCP 服务器配置
const claudeConfigPath = path.join(os.homedir(), '.claude.json');
const claudeConfig = JSON.parse(fs.readFileSync(claudeConfigPath, 'utf8'));

// 检查全局和项目特定的 MCP 服务器
const hasGlobalServers = claudeConfig.mcpServers && Object.keys(claudeConfig.mcpServers).length > 0;
const hasProjectServers = projectConfig && projectConfig.mcpServers && Object.keys(projectConfig.mcpServers).length > 0;
```

### 4. 终端集成 (server/index.js)

#### A. Shell WebSocket 处理

**直接终端访问：**
```javascript
// 构建 shell 命令
if (provider === 'claude') {
  if (hasSession && sessionId) {
    shellCommand = `cd "${projectPath}" && claude --resume ${sessionId} || claude`;
  } else {
    shellCommand = `cd "${projectPath}" && claude`;
  }
}

// 使用 node-pty 创建伪终端
shellProcess = pty.spawn(shell, shellArgs, {
  name: 'xterm-256color',
  cols: 80,
  rows: 24,
  cwd: projectPath,
  env: { ...process.env, TERM: 'xterm-256color' }
});
```

### 5. 前端集成 (src/components/ChatInterface.jsx)

#### A. 消息发送

**发送 Claude 命令：**
```javascript
sendMessage({
  type: 'claude-command',
  command: input,
  options: {
    projectPath: selectedProject.path,
    cwd: selectedProject.fullPath,
    sessionId: currentSessionId,
    resume: !!currentSessionId,
    toolsSettings: toolsSettings,
    permissionMode: permissionMode,
    images: uploadedImages
  }
});
```

#### B. 响应处理

**处理 Claude 响应：**
```javascript
// WebSocket 消息处理
useEffect(() => {
  if (messages.length > 0) {
    const latestMessage = messages[messages.length - 1];

    if (latestMessage.type === 'claude-response') {
      // 处理结构化响应
      handleClaudeResponse(latestMessage.data);
    } else if (latestMessage.type === 'claude-output') {
      // 处理原始输出
      handleRawOutput(latestMessage.data);
    }
  }
}, [messages]);
```

### 6. 使用的 Claude Code 功能总结

#### A. 核心 CLI 命令
1. **`claude`** - 基础聊天命令
2. **`claude --print <message>`** - 单次消息模式
3. **`claude --resume <session-id>`** - 恢复会话
4. **`claude --output-format stream-json`** - 流式 JSON 输出
5. **`claude --model sonnet`** - 指定模型
6. **`claude --permission-mode <mode>`** - 权限模式
7. **`claude --dangerously-skip-permissions`** - 跳过权限检查
8. **`claude --allowedTools <tool>`** - 允许特定工具
9. **`claude --disallowedTools <tool>`** - 禁用特定工具
10. **`claude --mcp-config <path>`** - MCP 配置文件

#### B. MCP 管理命令
1. **`claude mcp list`** - 列出 MCP 服务器
2. **`claude mcp add`** - 添加 MCP 服务器
3. **`claude mcp add-json`** - 通过 JSON 添加 MCP 服务器
4. **`claude mcp remove`** - 删除 MCP 服务器
5. **`claude mcp get`** - 获取 MCP 服务器详情

#### C. 文件系统集成
1. **项目发现** - 扫描 `~/.claude/projects/`
2. **会话解析** - 解析 `.jsonl` 会话文件
3. **配置管理** - 读写 `~/.claude.json` 和项目配置
4. **文件监控** - 实时监控项目文件变化

#### D. 高级功能
1. **图像处理** - 将上传的图像保存为临时文件并传递给 Claude
2. **会话保护** - 防止活跃会话期间的项目更新干扰
3. **跨平台支持** - Windows/macOS/Linux 兼容
4. **实时流式输出** - WebSocket 实时传输响应
5. **错误处理** - 完整的错误捕获和用户反馈

### 7. 数据流图

```
用户界面 → WebSocket → Express Server → Claude CLI Process
    ↑                                           ↓
    ←─── JSON Response ←─── Stream Parser ←─── stdout/stderr
```

这个项目实际上是 Claude Code CLI 的一个完整包装器，提供了比原生 CLI 更丰富的功能和更好的用户体验。
