import { Elysia } from "elysia"
import { config } from "./config.ts"
import { swagger } from "@elysiajs/swagger"
import { cors } from "@elysiajs/cors"
import { autoload } from "elysia-autoload"

export const app = new Elysia()
.state('config', config)
.use(swagger({
    path: "/swagger",
}))
.use(cors())
.use(await autoload({
    dir: "./routes",
    prefix: "/api",
    ignore: ["**/*.test.ts", "**/*.spec.ts"]
}))
.get("/", () => "Hello World")

export type ElysiaApp = typeof app